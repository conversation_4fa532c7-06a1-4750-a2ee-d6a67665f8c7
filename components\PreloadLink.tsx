'use client';

import Link from 'next/link';
import { ReactNode } from 'react';
import { clearCacheKey } from '../hooks/useApiCache';

interface PreloadLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  prefetch?: boolean;
  preloadData?: string[]; // قائمة بـ API endpoints للتحميل المسبق
  onClick?: () => void;
}

/**
 * مكون رابط محسن مع تحميل مسبق للبيانات
 */
const PreloadLink: React.FC<PreloadLinkProps> = ({
  href,
  children,
  className,
  prefetch = true,
  preloadData = [],
  onClick
}) => {

  // دالة للتحميل المسبق للبيانات
  const preloadApiData = async (endpoints: string[]) => {
    const promises = endpoints.map(async (endpoint) => {
      try {
        const response = await fetch(endpoint);
        if (response.ok) {
          await response.json();
          console.log(`✅ تم التحميل المسبق: ${endpoint}`);
        }
      } catch (error) {
        console.warn(`⚠️ فشل التحميل المسبق: ${endpoint}`, error);
      }
    });

    await Promise.allSettled(promises);
  };

  // معالج hover للتحميل المسبق
  const handleMouseEnter = () => {
    if (preloadData.length > 0) {
      preloadApiData(preloadData);
    }
  };

  // معالج النقر
  const handleClick = () => {
    if (onClick) {
      onClick();
    }

    // تنظيف الكاش القديم إذا لزم الأمر
    if (preloadData.length > 0) {
      preloadData.forEach(endpoint => {
        // مسح الكاش القديم للبيانات المحدثة
        clearCacheKey(endpoint);
      });
    }
  };

  return (
    <Link
      href={href}
      className={className}
      prefetch={prefetch}
      onMouseEnter={handleMouseEnter}
      onClick={handleClick}
    >
      {children}
    </Link>
  );
};

export default PreloadLink;
