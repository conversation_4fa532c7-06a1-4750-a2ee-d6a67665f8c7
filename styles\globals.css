:where([class^="ri-"])::before { content: "\f3c2"; }
body {
  direction: rtl;
  text-align: right;
  font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
}
.ltr {
direction: ltr;
text-align: left;
}
.rtl {
direction: rtl;
text-align: right;
}
.slider-container {
overflow: hidden;
position: relative;
}
.slider {
display: flex;
transition: transform 0.5s ease-in-out;
}
.slide {
min-width: 100%;
}
.slider-nav {
position: absolute;
bottom: 20px;
left: 50%;
transform: translateX(-50%);
display: flex;
gap: 8px;
}
.slider-dot {
width: 12px;
height: 12px;
border-radius: 50%;
background-color: rgba(255, 255, 255, 0.5);
cursor: pointer;
}
.slider-dot.active {
background-color: white;
}
.partners-slider {
animation: slidePartners 30s linear infinite;
}
@keyframes slidePartners {
0% { transform: translateX(0); }
100% { transform: translateX(-100%); }
}

/* تحسينات الناف بار للجوال */
.mobile-menu-item {
  position: relative;
  z-index: 10;
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.mobile-menu-item:active {
  transform: scale(0.98);
}

/* تحسين الضبابية للناف بار */
@supports (backdrop-filter: blur(12px)) {
  .navbar-blur {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }
}

/* تحسين التفاعل مع اللمس */
@media (hover: none) and (pointer: coarse) {
  .mobile-menu-item:hover {
    transform: none;
  }

  .mobile-menu-item:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

/* منع التمرير عند فتح القائمة */
body.mobile-menu-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* تحسين الناف بار للجوال */
@media (max-width: 1024px) {
  .navbar-mobile-menu {
    position: relative;
    z-index: 60;
  }

  .navbar-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 40;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  .navbar-mobile-content {
    position: relative;
    z-index: 50;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }
}

/* تحسين الأداء */
.navbar-optimized {
  will-change: transform, opacity;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* تحسين الانيميشن */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-item {
  animation: fadeInUp 0.3s ease-out forwards;
}

/* تحسين قائمة الفئات في الجوال */
.mobile-categories-list {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb #f9fafb;
}

.mobile-categories-list::-webkit-scrollbar {
  width: 4px;
}

.mobile-categories-list::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 2px;
}

.mobile-categories-list::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 2px;
}

.mobile-categories-list::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}

/* انيميشن للفئات الفرعية */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.mobile-subcategory {
  animation: slideInLeft 0.2s ease-out forwards;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
.category-item:hover .category-overlay {
opacity: 1;
}
.product-card:hover .product-actions {
opacity: 1;
}
.language-switch {
position: relative;
}
.language-dropdown {
display: none;
position: absolute;
top: 100%;
right: 0;
background-color: white;
border-radius: 8px;
box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
z-index: 50;
min-width: 120px;
}
.language-switch:hover .language-dropdown {
display: block;
}

/* تحسينات إضافية للتصميم */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.product-actions {
  transition: all 0.3s ease;
}

/* تأثيرات الأزرار */
.btn-primary {
  background: linear-gradient(135deg, #1B1B3A 0%, #4A4A7D 100%);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4A4A7D 0%, #1B1B3A 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(27, 27, 58, 0.3);
}

/* تحسين الانتقالات */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* تحسين التمرير */
html {
  scroll-behavior: smooth;
}

/* تحسين التركيز */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #1B1B3A;
  outline-offset: 2px;
}

/* تحسين الرسوم المتحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* أنيميشن الرسالة المنبثقة */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}
