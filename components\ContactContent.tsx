'use client';

import { useState } from 'react';
import { Locale } from '../lib/i18n';
import Footer from './Footer';
import WhatsAppButton from './WhatsAppButton';
import { useSiteSettings } from '../hooks/useSiteSettings';

interface ContactContentProps {
  locale: Locale;
}

export default function ContactContent({ locale }: ContactContentProps) {
  const { settings, loading } = useSiteSettings();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getContactContent = () => {
    if (!settings) {
      return {
        title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
        subtitle: locale === 'ar' ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك' : 'We are here to help you with all your inquiries and needs',
        formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
        name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
        email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
        phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
        subject: locale === 'ar' ? 'الموضوع' : 'Subject',
        message: locale === 'ar' ? 'الرسالة' : 'Message',
        send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
        sending: locale === 'ar' ? 'جاري الإرسال...' : 'Sending...',
        successMessage: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
        errorMessage: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.',
        contactInfo: locale === 'ar' ? 'معلومات التواصل' : 'Contact Information',
        address: locale === 'ar' ? 'العنوان' : 'Address',
        phone_label: locale === 'ar' ? 'الهاتف' : 'Phone',
        email_label: locale === 'ar' ? 'البريد الإلكتروني' : 'Email',
        hours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours'
      };
    }

    // استخدام الإعدادات الافتراضية
    return {
      title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
      subtitle: locale === 'ar'
        ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك'
        : 'We are here to help you with all your inquiries and needs',
      formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
      name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
      email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
      phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
      subject: locale === 'ar' ? 'الموضوع' : 'Subject',
      message: locale === 'ar' ? 'الرسالة' : 'Message',
      send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
      sending: locale === 'ar' ? 'جاري الإرسال...' : 'Sending...',
      successMessage: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
      errorMessage: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.',
      contactInfo: locale === 'ar' ? 'معلومات التواصل' : 'Contact Information',
      address: locale === 'ar' ? 'العنوان' : 'Address',
      phone_label: locale === 'ar' ? 'الهاتف' : 'Phone',
      email_label: locale === 'ar' ? 'البريد الإلكتروني' : 'Email',
      hours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours'
    };
  };

  const currentContent = getContactContent();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: ''
        });
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <>
        <main className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-primary animate-spin mb-4"></i>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        </main>
        <Footer locale={locale} />
      </>
    );
  }

  return (
    <>
      <main>
        {/* Hero Section */}
        <section className="bg-primary py-16">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {currentContent.title}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form & Info */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                  {currentContent.formTitle}
                </h2>

                {submitStatus === 'success' && (
                  <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                    <i className="ri-check-circle-line mr-2"></i>
                    {currentContent.successMessage}
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                    <i className="ri-error-warning-line mr-2"></i>
                    {currentContent.errorMessage}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {currentContent.name} *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                        placeholder={currentContent.name}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {currentContent.email} *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                        placeholder={currentContent.email}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {currentContent.phone}
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                        placeholder={currentContent.phone}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {currentContent.subject} *
                      </label>
                      <input
                        type="text"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                        placeholder={currentContent.subject}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {currentContent.message} *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                      placeholder={currentContent.message}
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-primary to-secondary text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <i className="ri-loader-4-line animate-spin"></i>
                        {currentContent.sending}
                      </>
                    ) : (
                      <>
                        <i className="ri-send-plane-line"></i>
                        {currentContent.send}
                      </>
                    )}
                  </button>
                </form>
              </div>

              {/* Contact Information */}
              <div className="space-y-8">
                <div className="bg-gray-50 rounded-2xl p-8">
                  <h3 className="text-2xl font-bold text-gray-800 mb-6">
                    {currentContent.contactInfo}
                  </h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-map-pin-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.address}</h4>
                        <p className="text-gray-600">
                          {locale === 'ar'
                            ? (settings?.addressAr || 'الرياض، المملكة العربية السعودية')
                            : (settings?.address || 'Riyadh, Saudi Arabia')
                          }
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-phone-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.phone_label}</h4>
                        <p className="text-gray-600">
                          {settings?.phone || '+966 XX XXX XXXX'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-mail-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.email_label}</h4>
                        <p className="text-gray-600">
                          {settings?.contactEmail || '<EMAIL>'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-time-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.hours}</h4>
                        <p className="text-gray-600">
                          {locale === 'ar'
                            ? (settings?.workingHoursAr || 'السبت - الخميس: 9:00 ص - 6:00 م')
                            : (settings?.workingHours || 'Saturday - Thursday: 9:00 AM - 6:00 PM')
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
