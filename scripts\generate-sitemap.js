const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

// دالة لإنشاء XML للـ sitemap
function generateSitemapXML(urls) {
  const urlsXML = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlsXML}
</urlset>`;
}

// دالة لإنشاء robots.txt
function generateRobotsTxt() {
  return `User-agent: *
Allow: /

Disallow: /admin/
Disallow: /api/
Disallow: /cart
Disallow: /checkout
Disallow: /profile
Disallow: /login
Disallow: /register
Disallow: /_next/
Disallow: /static/
Disallow: *.json
Disallow: *.xml

Sitemap: ${siteUrl}/sitemap.xml`;
}

// دالة رئيسية لإنشاء sitemap
async function generateSitemap() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    const currentTime = new Date().toISOString();
    const urls = [];

    // الصفحات الثابتة
    const staticPages = [
      { loc: `${siteUrl}/ar`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/en`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/ar/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/en/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/ar/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/en/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/ar/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/ar/contact`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/contact`, changefreq: 'monthly', priority: '0.6' },
    ];

    // إضافة الصفحات الثابتة
    staticPages.forEach(page => {
      urls.push({
        ...page,
        lastmod: currentTime,
      });
    });

    // جلب المنتجات
    console.log('🔄 Fetching products...');
    const [products] = await connection.execute(
      'SELECT id, updated_at FROM products WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
    );
    console.log(`✅ Found ${products.length} products`);

    products.forEach(product => {
      const lastmod = product.updated_at ? new Date(product.updated_at).toISOString() : currentTime;
      
      urls.push({
        loc: `${siteUrl}/ar/product/${product.id}`,
        changefreq: 'weekly',
        priority: '0.8',
        lastmod: lastmod,
      });
      
      urls.push({
        loc: `${siteUrl}/en/product/${product.id}`,
        changefreq: 'weekly',
        priority: '0.8',
        lastmod: lastmod,
      });
    });

    // جلب الفئات
    console.log('🔄 Fetching categories...');
    const [categories] = await connection.execute(
      'SELECT id, updated_at FROM categories WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
    );
    console.log(`✅ Found ${categories.length} categories`);

    categories.forEach(category => {
      const lastmod = category.updated_at ? new Date(category.updated_at).toISOString() : currentTime;
      
      urls.push({
        loc: `${siteUrl}/ar/category/${category.id}`,
        changefreq: 'weekly',
        priority: '0.7',
        lastmod: lastmod,
      });
      
      urls.push({
        loc: `${siteUrl}/en/category/${category.id}`,
        changefreq: 'weekly',
        priority: '0.7',
        lastmod: lastmod,
      });
    });

    // جلب الفئات الفرعية
    console.log('🔄 Fetching subcategories...');
    const [subcategories] = await connection.execute(
      'SELECT id, updated_at FROM subcategories WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
    );
    console.log(`✅ Found ${subcategories.length} subcategories`);

    subcategories.forEach(subcategory => {
      const lastmod = subcategory.updated_at ? new Date(subcategory.updated_at).toISOString() : currentTime;
      
      urls.push({
        loc: `${siteUrl}/ar/subcategory/${subcategory.id}`,
        changefreq: 'weekly',
        priority: '0.6',
        lastmod: lastmod,
      });
      
      urls.push({
        loc: `${siteUrl}/en/subcategory/${subcategory.id}`,
        changefreq: 'weekly',
        priority: '0.6',
        lastmod: lastmod,
      });
    });

    // إنشاء sitemap.xml
    const sitemapXML = generateSitemapXML(urls);
    const sitemapPath = path.join(process.cwd(), 'public', 'sitemap.xml');
    fs.writeFileSync(sitemapPath, sitemapXML);
    console.log(`✅ Generated sitemap.xml with ${urls.length} URLs`);

    // إنشاء robots.txt
    const robotsTxt = generateRobotsTxt();
    const robotsPath = path.join(process.cwd(), 'public', 'robots.txt');
    fs.writeFileSync(robotsPath, robotsTxt);
    console.log('✅ Generated robots.txt');

    console.log('🎉 Sitemap generation completed successfully!');

  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// تشغيل الدالة
if (require.main === module) {
  generateSitemap();
}

module.exports = { generateSitemap };
