import { NextResponse } from 'next/server';
import {
  getProducts,
  getCategories,
  getSubcategories,
  getAdmins,
  getFeaturedProducts
} from '@/lib/mysql-database';

export async function GET() {
  try {
    console.log('🔄 جلب إحصائيات لوحة التحكم...');

    // جلب جميع البيانات بشكل متوازي
    const [
      allProducts,
      allCategories,
      allSubcategories,
      allAdmins,
      featuredProducts
    ] = await Promise.all([
      getProducts(),
      getCategories(),
      getSubcategories(),
      getAdmins(),
      getFeaturedProducts()
    ]);

    console.log('📊 البيانات المجلبة:', {
      products: allProducts.length,
      categories: allCategories.length,
      subcategories: allSubcategories.length,
      admins: allAdmins.length,
      featured: featuredProducts.length
    });

    // حساب الإحصائيات
    const activeProducts = allProducts.filter(p => p.is_active).length;
    const inactiveProducts = allProducts.filter(p => !p.is_active).length;
    const activeCategories = allCategories.filter(c => c.is_active).length;
    const activeSubcategories = allSubcategories.filter(s => s.is_active).length;

    const stats = {
      totalProducts: allProducts.length,
      activeProducts,
      inactiveProducts,
      featuredProducts: featuredProducts.length,
      totalCategories: allCategories.length,
      activeCategories,
      totalSubcategories: allSubcategories.length,
      activeSubcategories,
      totalAdmins: allAdmins.length
    };

    console.log('✅ الإحصائيات النهائية:', stats);

    return NextResponse.json({
      success: true,
      message: 'Dashboard stats fetched successfully',
      messageAr: 'تم جلب إحصائيات لوحة التحكم بنجاح',
      stats
    });

  } catch (error: unknown) {
    console.error('❌ خطأ في جلب إحصائيات لوحة التحكم:', error);

    let errorMessage = 'Unknown error';
    let errorStack = undefined;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack;
    }

    return NextResponse.json({
      success: false,
      message: 'Failed to fetch dashboard stats',
      messageAr: 'فشل في جلب إحصائيات لوحة التحكم',
      error: {
        message: errorMessage,
        stack: errorStack
      }
    }, { status: 500 });
  }
}
