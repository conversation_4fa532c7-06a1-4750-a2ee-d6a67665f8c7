import { NextRequest, NextResponse } from 'next/server';
import { getSubcategoryById, updateSubcategory, deleteSubcategory } from '@/lib/mysql-database';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid subcategory ID',
        messageAr: 'معرف الفئة الفرعية غير صحيح'
      }, { status: 400 });
    }

    const subcategory = await getSubcategoryById(id);
    if (!subcategory) {
      return NextResponse.json({
        success: false,
        error: 'Subcategory not found',
        messageAr: 'الفئة الفرعية غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: subcategory
    });
  } catch (error) {
    console.error('Subcategory GET API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const updates = await request.json();

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid subcategory ID',
        messageAr: 'معرف الفئة الفرعية غير صحيح'
      }, { status: 400 });
    }

    const updatedSubcategory = await updateSubcategory(id, updates);
    if (!updatedSubcategory) {
      return NextResponse.json({
        success: false,
        error: 'Subcategory not found',
        messageAr: 'الفئة الفرعية غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: updatedSubcategory,
      message: 'Subcategory updated successfully',
      messageAr: 'تم تحديث الفئة الفرعية بنجاح'
    });
  } catch (error) {
    console.error('Subcategory PUT API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid subcategory ID',
        messageAr: 'معرف الفئة الفرعية غير صحيح'
      }, { status: 400 });
    }

    const deleted = await deleteSubcategory(id);
    if (!deleted) {
      return NextResponse.json({
        success: false,
        error: 'Subcategory not found',
        messageAr: 'الفئة الفرعية غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Subcategory deleted successfully',
      messageAr: 'تم حذف الفئة الفرعية بنجاح'
    });
  } catch (error) {
    console.error('Subcategory DELETE API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
