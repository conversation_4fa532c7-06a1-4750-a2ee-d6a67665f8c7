'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import Footer from './Footer';
import WhatsAppButton from './WhatsAppButton';
import toast from 'react-hot-toast';
import { useCart } from '../lib/session-cart';

interface CartContentProps {
  locale: Locale;
}

export default function CartContent({ locale }: CartContentProps) {
  // استخدام النظام الجديد للعربة
  const { cart, updateQuantity, removeFromCart, clearCart, total } = useCart();

  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
  });

  const content = {
    ar: {
      title: 'سلة التسوق',
      subtitle: 'راجع المنتجات المختارة واطلب عرض سعر',
      empty: 'السلة فارغة',
      emptyMessage: 'لم تقم بإضافة أي منتجات إلى السلة بعد',
      continueShopping: 'متابعة التسوق',
      product: 'المنتج',
      quantity: 'الكمية',
      price: 'السعر',
      total: 'المجموع',
      remove: 'حذف',
      clear: 'إفراغ السلة',
      requestQuote: 'طلب عرض سعر',
      name: 'الاسم الكامل',
      email: 'البريد الإلكتروني',
      phone: 'رقم الهاتف',
      company: 'اسم الشركة (اختياري)',
      submit: 'إرسال الطلب',
      cancel: 'إلغاء',
      success: 'تم إرسال طلبك بنجاح! سنتواصل معك قريباً.',
      error: 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.'
    },
    en: {
      title: 'Shopping Cart',
      subtitle: 'Review selected products and request a quote',
      empty: 'Cart is empty',
      emptyMessage: 'You haven\'t added any products to your cart yet',
      continueShopping: 'Continue Shopping',
      product: 'Product',
      quantity: 'Quantity',
      price: 'Price',
      total: 'Total',
      remove: 'Remove',
      clear: 'Clear Cart',
      requestQuote: 'Request Quote',
      name: 'Full Name',
      email: 'Email Address',
      phone: 'Phone Number',
      company: 'Company Name (Optional)',
      submit: 'Submit Request',
      cancel: 'Cancel',
      success: 'Your request has been sent successfully! We will contact you soon.',
      error: 'An error occurred while sending the request. Please try again.'
    }
  };

  const t = content[locale];

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(id);
      toast.success(locale === 'ar' ? 'تم حذف المنتج من السلة' : 'Product removed from cart');
    } else {
      updateQuantity(id, newQuantity);
    }
  };

  const handleRemoveItem = (id: string) => {
    removeFromCart(id);
    toast.success(locale === 'ar' ? 'تم حذف المنتج من السلة' : 'Product removed from cart');
  };

  const handleClearCart = () => {
    clearCart();
    toast.success(locale === 'ar' ? 'تم إفراغ السلة' : 'Cart cleared');
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/quote-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...form,
          cart: cart,
          total: total,
          locale: locale
        }),
      });

      if (response.ok) {
        toast.success(t.success);
        setShowForm(false);
        setForm({ name: '', email: '', phone: '', company: '' });
        clearCart();
      } else {
        toast.error(t.error);
      }
    } catch (error) {
      console.error('Error submitting quote request:', error);
      toast.error(t.error);
    }
  };

  return (
    <>
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-2">
              {t.title}
            </h1>
            <p className="text-gray-600">
              {t.subtitle}
            </p>
          </div>

          {cart.length === 0 ? (
            /* Empty Cart */
            <div className="text-center py-16">
              <div className="text-6xl text-gray-300 mb-4">
                <i className="ri-shopping-cart-line"></i>
              </div>
              <h2 className="text-2xl font-bold text-gray-600 mb-2">
                {t.empty}
              </h2>
              <p className="text-gray-500 mb-8">
                {t.emptyMessage}
              </p>
              <Link
                href={`/${locale}/products`}
                className="bg-gradient-to-r from-primary to-secondary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 inline-flex items-center gap-2"
              >
                <i className="ri-shopping-bag-line"></i>
                {t.continueShopping}
              </Link>
            </div>
          ) : (
            /* Cart Items */
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Cart Items List */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                      <h2 className="text-xl font-bold text-gray-800">
                        {t.product} ({cart.length})
                      </h2>
                      <button
                        onClick={handleClearCart}
                        className="text-red-600 hover:text-red-800 transition-colors text-sm font-medium"
                      >
                        <i className="ri-delete-bin-line mr-1"></i>
                        {t.clear}
                      </button>
                    </div>
                  </div>

                  <div className="divide-y divide-gray-200">
                    {cart.map((item) => (
                      <div key={item.id} className="p-6">
                        <div className="flex items-center gap-4">
                          <div className="relative w-20 h-20 flex-shrink-0">
                            <Image
                              src={item.image || '/api/placeholder?width=80&height=80&text=No Image'}
                              alt={item.title}
                              fill
                              className="object-cover rounded-lg"
                            />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-800 truncate">
                              {item.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {locale === 'ar' ? 'السعر:' : 'Price:'} {item.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                            </p>
                          </div>

                          <div className="flex items-center gap-3">
                            <div className="flex items-center border border-gray-300 rounded-lg">
                              <button
                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                className="p-2 hover:bg-gray-100 transition-colors"
                              >
                                <i className="ri-subtract-line"></i>
                              </button>
                              <span className="px-4 py-2 font-medium">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                className="p-2 hover:bg-gray-100 transition-colors"
                              >
                                <i className="ri-add-line"></i>
                              </button>
                            </div>

                            <button
                              onClick={() => handleRemoveItem(item.id)}
                              className="p-2 text-red-600 hover:text-red-800 transition-colors"
                            >
                              <i className="ri-delete-bin-line"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Cart Summary */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
                  <h3 className="text-xl font-bold text-gray-800 mb-6">
                    {t.total}
                  </h3>
                  
                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>{t.total}:</span>
                      <span className="text-primary">{total} {locale === 'ar' ? 'ريال' : 'SAR'}</span>
                    </div>
                  </div>

                  <button
                    onClick={() => setShowForm(true)}
                    className="w-full bg-gradient-to-r from-primary to-secondary text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                  >
                    <i className="ri-file-text-line"></i>
                    {t.requestQuote}
                  </button>

                  <Link
                    href={`/${locale}/products`}
                    className="w-full mt-4 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 flex items-center justify-center gap-2"
                  >
                    <i className="ri-shopping-bag-line"></i>
                    {t.continueShopping}
                  </Link>
                </div>
              </div>
            </div>
          )}

          {/* Quote Request Form Modal */}
          {showForm && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-2xl p-8 max-w-md w-full max-h-[90vh] overflow-y-auto">
                <h3 className="text-2xl font-bold text-gray-800 mb-6">
                  {t.requestQuote}
                </h3>

                <form onSubmit={handleFormSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t.name} *
                    </label>
                    <input
                      type="text"
                      value={form.name}
                      onChange={(e) => setForm({ ...form, name: e.target.value })}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t.email} *
                    </label>
                    <input
                      type="email"
                      value={form.email}
                      onChange={(e) => setForm({ ...form, email: e.target.value })}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t.phone} *
                    </label>
                    <input
                      type="tel"
                      value={form.phone}
                      onChange={(e) => setForm({ ...form, phone: e.target.value })}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t.company}
                    </label>
                    <input
                      type="text"
                      value={form.company}
                      onChange={(e) => setForm({ ...form, company: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                    />
                  </div>

                  <div className="flex gap-4 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowForm(false)}
                      className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                    >
                      {t.cancel}
                    </button>
                    <button
                      type="submit"
                      className="flex-1 bg-gradient-to-r from-primary to-secondary text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"
                    >
                      {t.submit}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
