import { NextResponse } from 'next/server';
import { serialize } from 'cookie';

// POST - تسجيل الخروج
export async function POST() {
  try {
    // حذف cookie المصادقة
    const cookie = serialize('authToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // انتهاء فوري
      path: '/'
    });
    
    // إنشاء الاستجابة
    const response = NextResponse.json({
      success: true,
      message: 'Logout successful',
      messageAr: 'تم تسجيل الخروج بنجاح'
    });

    // إضافة cookie إلى الاستجابة
    response.headers.set('Set-Cookie', cookie);
    
    return response;
    
  } catch (error) {
    console.error('Logout API error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
