import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/database-config';

export async function GET() {
  try {
    console.log('🔍 اختبار الاتصال بقاعدة البيانات...');
    
    const connection = await getConnection();
    
    try {
      // اختبار الاتصال
      await connection.execute('SELECT 1 as test');
      console.log('✅ الاتصال بقاعدة البيانات ناجح');
      
      // اختبار جدول المنتجات
      const [products] = await connection.execute('SELECT COUNT(*) as count FROM products');
      console.log('📦 عدد المنتجات:', products);
      
      // اختبار جدول الفئات
      const [categories] = await connection.execute('SELECT COUNT(*) as count FROM categories');
      console.log('📂 عدد الفئات:', categories);
      
      return NextResponse.json({
        success: true,
        message: 'Database connection successful',
        data: {
          connection: 'OK',
          products: products,
          categories: categories
        }
      });
      
    } finally {
      connection.release();
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
