import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';
import { invalidateAll, invalidateProducts, invalidateCategories, invalidateSubcategories } from '@/lib/cache-manager';

/**
 * API لإعادة التحقق من الكاش يدوياً من لوحة الإدارة
 */

export async function POST(request: NextRequest) {
  try {
    const { type, tags, paths } = await request.json();

    console.log('🔄 طلب إعادة التحقق من الكاش:', { type, tags, paths });

    switch (type) {
      case 'all':
        invalidateAll();
        break;

      case 'products':
        invalidateProducts();
        break;

      case 'categories':
        invalidateCategories();
        break;

      case 'subcategories':
        invalidateSubcategories();
        break;

      case 'custom':
        // إعادة التحقق من علامات مخصصة
        if (tags && Array.isArray(tags)) {
          tags.forEach((tag: string) => {
            revalidateTag(tag);
            console.log(`✅ تم إعادة التحقق من العلامة: ${tag}`);
          });
        }

        // إعادة التحقق من مسارات مخصصة
        if (paths && Array.isArray(paths)) {
          paths.forEach((path: string) => {
            revalidatePath(path);
            console.log(`✅ تم إعادة التحقق من المسار: ${path}`);
          });
        }
        break;

      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid revalidation type',
          messageAr: 'نوع إعادة التحقق غير صالح'
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'Cache revalidated successfully',
      messageAr: 'تم إعادة التحقق من الكاش بنجاح',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ خطأ في إعادة التحقق من الكاش:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to revalidate cache',
      messageAr: 'فشل في إعادة التحقق من الكاش',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET endpoint لعرض معلومات الكاش
export async function GET() {
  try {
    const cacheInfo = {
      lastRevalidated: new Date().toISOString(),
      availableTypes: [
        { type: 'all', description: 'إعادة التحقق من جميع البيانات' },
        { type: 'products', description: 'إعادة التحقق من المنتجات' },
        { type: 'categories', description: 'إعادة التحقق من الفئات' },
        { type: 'subcategories', description: 'إعادة التحقق من الفئات الفرعية' },
        { type: 'custom', description: 'إعادة التحقق المخصص' }
      ],
      availableTags: [
        'products',
        'categories', 
        'subcategories',
        'featured',
        'settings',
        'quotes',
        'admins'
      ],
      availablePaths: [
        '/',
        '/products',
        '/admin',
        '/admin/products',
        '/admin/categories',
        '/admin/subcategories'
      ]
    };

    return NextResponse.json({
      success: true,
      data: cacheInfo
    });

  } catch (error) {
    console.error('❌ خطأ في جلب معلومات الكاش:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to get cache info',
      messageAr: 'فشل في جلب معلومات الكاش'
    }, { status: 500 });
  }
}
