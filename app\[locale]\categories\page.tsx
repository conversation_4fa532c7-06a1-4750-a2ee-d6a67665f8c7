import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getPageSEO } from '../../../lib/seo.config';
import CategoriesPageComponent from '../../../components/CategoriesPage';

interface PageProps {
  params: Promise<{
    locale: Locale;
  }>;
}

// إنشاء metadata لصفحة الفئات
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;

  const seoData = getPageSEO(locale, 'categories');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}/categories`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-categories.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-categories.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}/categories`,
      languages: {
        'ar': `${baseUrl}/ar/categories`,
        'en': `${baseUrl}/en/categories`,
      },
    },
  };
}

export default async function Categories({ params }: PageProps) {
  const { locale } = await params;

  return <CategoriesPageComponent locale={locale} />;
}
