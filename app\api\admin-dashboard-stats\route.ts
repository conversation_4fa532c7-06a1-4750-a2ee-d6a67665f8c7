import { NextResponse } from 'next/server';
import { executeQuery } from '../../../lib/database-config';

// تعريف أنواع البيانات
interface StatsRow {
  total: number;
  active: number;
  featured?: number;
}

interface ProductRow {
  id: number;
  title: string;
  title_ar: string;
  price: number;
  is_active: boolean;
  is_featured: boolean;
  created_at: Date;
}

interface QuoteStats {
  total: number;
  pending: number;
  processed: number;
  today: number;
  this_week: number;
}

interface SystemStats {
  uptime: number;
  memory: NodeJS.MemoryUsage;
  platform: string;
  nodeVersion: string;
}

export async function GET() {
  try {
    console.log('🔄 جلب إحصائيات لوحة التحكم...');
    // إحصائيات المنتجات
    const productsResult = await executeQuery<StatsRow>(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured
      FROM products
      WHERE deleted_at IS NULL
    `);

    // إحصائيات الفئات
    const categoriesResult = await executeQuery<StatsRow>(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
      FROM categories
      WHERE deleted_at IS NULL
    `);

    // إحصائيات الفئات الفرعية
    const subcategoriesResult = await executeQuery<StatsRow>(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
      FROM subcategories
      WHERE deleted_at IS NULL
    `);

    // أحدث المنتجات
    const recentProductsResult = await executeQuery<ProductRow>(`
      SELECT
        id,
        title,
        title_ar,
        price,
        is_active,
        is_featured,
        created_at
      FROM products
      WHERE deleted_at IS NULL
      ORDER BY created_at DESC
      LIMIT 5
    `);

    // إحصائيات أساسية لطلبات التسعير
    const quoteStats: QuoteStats = {
      total: 0,
      pending: 0,
      processed: 0,
      today: 0,
      this_week: 0
    };

    // إحصائيات النظام
    const systemStats: SystemStats = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      platform: process.platform,
      nodeVersion: process.version
    };

    const stats = {
      products: Array.isArray(productsResult) && productsResult.length > 0 ? productsResult[0] : { total: 0, active: 0, featured: 0 },
      categories: Array.isArray(categoriesResult) && categoriesResult.length > 0 ? categoriesResult[0] : { total: 0, active: 0 },
      subcategories: Array.isArray(subcategoriesResult) && subcategoriesResult.length > 0 ? subcategoriesResult[0] : { total: 0, active: 0 },
      quotes: quoteStats,
      recentProducts: recentProductsResult || [],
      system: systemStats,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };

    console.log('✅ تم جلب الإحصائيات بنجاح');

    return NextResponse.json({
      success: true,
      data: stats
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('❌ خطأ في جلب إحصائيات لوحة التحكم:', error);

    // تحديد نوع الخطأ لإرجاع رسالة مناسبة
    const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch dashboard statistics',
      messageAr: 'فشل في جلب إحصائيات لوحة التحكم',
      details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
      timestamp: new Date().toISOString()
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
}
