'use client';

import { useEffect } from 'react';
import { initializePagePerformance } from '../lib/page-performance';

/**
 * مكون لتهيئة تحسينات الأداء
 */
const PerformanceInitializer: React.FC = () => {
  useEffect(() => {
    // تهيئة تحسينات الأداء عند تحميل التطبيق
    const cleanup = initializePagePerformance();

    // تحسين الخطوط - استخدام Google Fonts المحملة مسبقاً
    if (typeof window !== 'undefined') {
      // تحسين التمرير السلس
      document.documentElement.style.scrollBehavior = 'smooth';

      // تحسين عرض الخطوط - إضافة preconnect لـ Google Fonts إذا لم يكن موجوداً
      const preconnectLinks = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com'
      ];

      preconnectLinks.forEach(href => {
        if (!document.querySelector(`link[href="${href}"]`)) {
          const link = document.createElement('link');
          link.rel = 'preconnect';
          link.href = href;
          link.crossOrigin = 'anonymous';
          document.head.appendChild(link);
        }
      });

      // تحسين focus للوصولية
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
          document.body.classList.add('keyboard-navigation');
        }
      });

      document.addEventListener('mousedown', () => {
        document.body.classList.remove('keyboard-navigation');
      });

      // تحسين الصور الكسولة
      const images = document.querySelectorAll('img[loading="lazy"]');
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
              }
            }
          });
        });

        images.forEach((img) => imageObserver.observe(img));
      }

      // تحسين الذاكرة - تنظيف عند إغلاق الصفحة
      const cleanupHandler = () => {
        // تنظيف event listeners
        // مسح timers
        // تنظيف caches
      };

      window.addEventListener('beforeunload', cleanupHandler);
      
      return () => {
        if (cleanup) cleanup();
        window.removeEventListener('beforeunload', cleanupHandler);
      };
    }
  }, []);

  // هذا المكون لا يعرض أي شيء - فقط يقوم بالتهيئة
  return null;
};

export default PerformanceInitializer;
