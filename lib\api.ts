// خدمات API للعمل مع البيانات من جانب العميل
import { Category, Subcategory, Product } from '../types/mysql-database';
import { SiteSettings } from '../types/admin';

const API_BASE = '/api';

// خدمات الفئات
export const categoriesApi = {
  // الحصول على جميع الفئات
  getAll: async (): Promise<Category[]> => {
    const response = await fetch(`${API_BASE}/categories`);
    if (!response.ok) throw new Error('Failed to fetch categories');
    return response.json();
  },

  // الحصول على فئة بواسطة ID
  getById: async (id: string): Promise<Category> => {
    const response = await fetch(`${API_BASE}/categories/${id}`);
    if (!response.ok) throw new Error('Failed to fetch category');
    return response.json();
  },

  // إضافة فئة جديدة
  create: async (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> => {
    const response = await fetch(`${API_BASE}/categories`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(category)
    });
    if (!response.ok) throw new Error('Failed to create category');
    return response.json();
  },

  // تحديث فئة
  update: async (id: string, updates: Partial<Category>): Promise<Category> => {
    const response = await fetch(`${API_BASE}/categories/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    if (!response.ok) throw new Error('Failed to update category');
    return response.json();
  },

  // حذف فئة
  delete: async (id: string): Promise<void> => {
    const response = await fetch(`${API_BASE}/categories/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete category');
  }
};

// خدمات الفئات الفرعية
export const subcategoriesApi = {
  // الحصول على جميع الفئات الفرعية
  getAll: async (): Promise<Subcategory[]> => {
    const response = await fetch(`${API_BASE}/subcategories`);
    if (!response.ok) throw new Error('Failed to fetch subcategories');
    return response.json();
  },

  // الحصول على الفئات الفرعية بواسطة فئة
  getByCategory: async (categoryId: string): Promise<Subcategory[]> => {
    const response = await fetch(`${API_BASE}/subcategories?categoryId=${categoryId}`);
    if (!response.ok) throw new Error('Failed to fetch subcategories');
    return response.json();
  },

  // الحصول على فئة فرعية بواسطة ID
  getById: async (id: string): Promise<Subcategory> => {
    const response = await fetch(`${API_BASE}/subcategories/${id}`);
    if (!response.ok) throw new Error('Failed to fetch subcategory');
    return response.json();
  },

  // إضافة فئة فرعية جديدة
  create: async (subcategory: Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<Subcategory> => {
    const response = await fetch(`${API_BASE}/subcategories`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(subcategory)
    });
    if (!response.ok) throw new Error('Failed to create subcategory');
    return response.json();
  },

  // تحديث فئة فرعية
  update: async (id: string, updates: Partial<Subcategory>): Promise<Subcategory> => {
    const response = await fetch(`${API_BASE}/subcategories/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    if (!response.ok) throw new Error('Failed to update subcategory');
    return response.json();
  },

  // حذف فئة فرعية
  delete: async (id: string): Promise<void> => {
    const response = await fetch(`${API_BASE}/subcategories/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete subcategory');
  }
};

// خدمات المنتجات
export const productsApi = {
  // الحصول على جميع المنتجات
  getAll: async (): Promise<Product[]> => {
    const response = await fetch(`${API_BASE}/products`);
    if (!response.ok) throw new Error('Failed to fetch products');
    return response.json();
  },

  // الحصول على المنتجات المميزة
  getFeatured: async (): Promise<Product[]> => {
    const response = await fetch(`${API_BASE}/products?featured=true`);
    if (!response.ok) throw new Error('Failed to fetch featured products');
    return response.json();
  },

  // الحصول على المنتجات بواسطة الفئة
  getByCategory: async (categoryId: string): Promise<Product[]> => {
    const response = await fetch(`${API_BASE}/products?categoryId=${categoryId}`);
    if (!response.ok) throw new Error('Failed to fetch products');
    return response.json();
  },

  // الحصول على المنتجات بواسطة الفئة الفرعية
  getBySubcategory: async (subcategoryId: string): Promise<Product[]> => {
    const response = await fetch(`${API_BASE}/products?subcategoryId=${subcategoryId}`);
    if (!response.ok) throw new Error('Failed to fetch products');
    return response.json();
  },

  // الحصول على منتج بواسطة ID
  getById: async (id: string): Promise<Product> => {
    const response = await fetch(`${API_BASE}/products/${id}`);
    if (!response.ok) throw new Error('Failed to fetch product');
    return response.json();
  },

  // البحث في المنتجات
  search: async (query: string): Promise<Product[]> => {
    const response = await fetch(`${API_BASE}/products/search?q=${encodeURIComponent(query)}`);
    if (!response.ok) throw new Error('Failed to search products');
    return response.json();
  },

  // إضافة منتج جديد
  create: async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
    const response = await fetch(`${API_BASE}/products`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(product)
    });
    if (!response.ok) throw new Error('Failed to create product');
    return response.json();
  },

  // تحديث منتج
  update: async (id: string, updates: Partial<Product>): Promise<Product> => {
    const response = await fetch(`${API_BASE}/products/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    if (!response.ok) throw new Error('Failed to update product');
    return response.json();
  },

  // حذف منتج
  delete: async (id: string): Promise<void> => {
    const response = await fetch(`${API_BASE}/products/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete product');
  }
};

// خدمات إعدادات الموقع
export const settingsApi = {
  // الحصول على إعدادات الموقع
  get: async (): Promise<SiteSettings> => {
    const response = await fetch(`${API_BASE}/settings`);
    if (!response.ok) throw new Error('Failed to fetch settings');
    return response.json();
  },

  // تحديث إعدادات الموقع
  update: async (settings: Partial<SiteSettings>): Promise<SiteSettings> => {
    const response = await fetch(`${API_BASE}/settings`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(settings)
    });
    if (!response.ok) throw new Error('Failed to update settings');
    return response.json();
  }
};
