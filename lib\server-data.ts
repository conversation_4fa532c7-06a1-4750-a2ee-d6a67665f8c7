import { ProductWithDetails, Category, Subcategory } from '../types/mysql-database';
import { getCategories as getCategoriesFromDB, getFeaturedProductsWithDetails } from './mysql-database';

// Base URL للـ API
const getBaseUrl = () => {
  if (process.env.NODE_ENV === 'production') {
    return process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  }
  return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
};

// دالة مساعدة لجلب البيانات مع إعدادات الكاش
async function fetchWithCache(url: string, options: RequestInit = {}) {
  const baseUrl = getBaseUrl();
  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;
  
  return fetch(fullUrl, {
    ...options,
    next: { revalidate: 60 }, // إعادة التحقق كل دقيقة
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });
}

// جلب جميع المنتجات
export async function getProducts(): Promise<ProductWithDetails[]> {
  try {
    const response = await fetchWithCache('/api/products');
    if (!response.ok) {
      console.error('Failed to fetch products:', response.status);
      return [];
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      return result.data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

// جلب المنتجات حسب الفئة
export async function getProductsByCategory(categoryId: string): Promise<ProductWithDetails[]> {
  try {
    const response = await fetchWithCache(`/api/products?categoryId=${categoryId}`);
    if (!response.ok) {
      console.error('Failed to fetch products by category:', response.status);
      return [];
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      return result.data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching products by category:', error);
    return [];
  }
}

// جلب المنتجات حسب الفئة الفرعية
export async function getProductsBySubcategory(subcategoryId: string): Promise<ProductWithDetails[]> {
  try {
    const response = await fetchWithCache(`/api/products?subcategoryId=${subcategoryId}`);
    if (!response.ok) {
      console.error('Failed to fetch products by subcategory:', response.status);
      return [];
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      return result.data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching products by subcategory:', error);
    return [];
  }
}

// جلب منتج واحد
export async function getProduct(id: string): Promise<ProductWithDetails | null> {
  try {
    const response = await fetchWithCache(`/api/products/${id}`);
    if (!response.ok) {
      console.error('Failed to fetch product:', response.status);
      return null;
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      return result.data;
    }
    return null;
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

// جلب جميع الفئات
export async function getCategories(): Promise<Category[]> {
  try {
    // استخدام دالة قاعدة البيانات مباشرة في Server Components
    const categories = await getCategoriesFromDB();
    console.log('📦 تم جلب الفئات من قاعدة البيانات:', categories.length);
    return categories;
  } catch (error) {
    console.error('Error fetching categories from database:', error);
    return [];
  }
}

// جلب فئة واحدة
export async function getCategory(id: string): Promise<Category | null> {
  try {
    const response = await fetchWithCache(`/api/categories?id=${id}`, {
      next: { revalidate: 300 },
    });
    if (!response.ok) {
      console.error('Failed to fetch category:', response.status);
      return null;
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      return result.data;
    }
    return null;
  } catch (error) {
    console.error('Error fetching category:', error);
    return null;
  }
}

// جلب الفئات الفرعية حسب الفئة
export async function getSubcategoriesByCategory(categoryId: string): Promise<Subcategory[]> {
  try {
    const response = await fetchWithCache(`/api/subcategories?categoryId=${categoryId}`, {
      next: { revalidate: 300 },
    });
    if (!response.ok) {
      console.error('Failed to fetch subcategories:', response.status);
      return [];
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      // تصفية الفئات الفرعية النشطة فقط
      return result.data.filter((sub: Subcategory) => sub.is_active);
    }
    return [];
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return [];
  }
}

// جلب فئة فرعية واحدة
export async function getSubcategory(id: string): Promise<Subcategory | null> {
  try {
    const response = await fetchWithCache(`/api/subcategories/${id}`, {
      next: { revalidate: 300 },
    });
    if (!response.ok) {
      console.error('Failed to fetch subcategory:', response.status);
      return null;
    }
    
    const result = await response.json();
    if (result.success && result.data) {
      return result.data;
    }
    return null;
  } catch (error) {
    console.error('Error fetching subcategory:', error);
    return null;
  }
}

// جلب المنتجات المميزة
export async function getFeaturedProducts(): Promise<ProductWithDetails[]> {
  try {
    // استخدام دالة قاعدة البيانات مباشرة في Server Components
    const products = await getFeaturedProductsWithDetails();
    console.log('📦 تم جلب المنتجات المميزة من قاعدة البيانات:', products.length);
    return products;
  } catch (error) {
    console.error('Error fetching featured products from database:', error);
    return [];
  }
}
