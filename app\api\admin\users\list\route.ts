import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { executeQuery } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
}

// التحقق من صحة JWT token
function verifyToken(token: string): { userId: number } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: number };
  } catch {
    return null;
  }
}

// استخراج token من الطلب
async function extractToken(req: NextRequest): Promise<string | null> {
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const cookieStore = await cookies();
  const tokenFromCookie = cookieStore.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function GET(req: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = await extractToken(req);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    // الحصول على قائمة جميع المستخدمين
    const users = await executeQuery<AdminUser>(
      `SELECT id, username, email, is_active, last_login, created_at, updated_at
       FROM admins
       WHERE deleted_at IS NULL
       ORDER BY created_at DESC`
    );

    // تنسيق البيانات
    const formattedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: 'admin',
      isActive: user.is_active,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    }));

    return NextResponse.json({
      success: true,
      users: formattedUsers,
      total: formattedUsers.length
    });

  } catch (error) {
    console.error('Get users list error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
