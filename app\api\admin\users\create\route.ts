import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { executeQuery, executeQuerySingle } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string): { userId: number } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: number };
  } catch {
    return null;
  }
}

// استخراج token من الطلب
async function extractToken(req: NextRequest): Promise<string | null> {
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const cookieStore = await cookies();
  const tokenFromCookie = cookieStore.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function POST(req: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = await extractToken(req);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    const { username, email, password } = await req.json();

    // التحقق من وجود البيانات المطلوبة
    if (!username || !email || !password) {
      return NextResponse.json({
        message: 'جميع الحقول مطلوبة',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        message: 'البريد الإلكتروني غير صحيح',
        success: false
      }, { status: 400 });
    }

    // التحقق من قوة كلمة المرور
    if (password.length < 6) {
      return NextResponse.json({
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        success: false
      }, { status: 400 });
    }

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    const existingUser = await executeQuerySingle(
      'SELECT id FROM admins WHERE username = ? AND deleted_at IS NULL',
      [username]
    );

    if (existingUser) {
      return NextResponse.json({
        message: 'اسم المستخدم موجود بالفعل',
        success: false
      }, { status: 409 });
    }

    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    const existingEmail = await executeQuerySingle(
      'SELECT id FROM admins WHERE email = ? AND deleted_at IS NULL',
      [email]
    );

    if (existingEmail) {
      return NextResponse.json({
        message: 'البريد الإلكتروني موجود بالفعل',
        success: false
      }, { status: 409 });
    }

    // تشفير كلمة المرور
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // إدراج المستخدم الجديد
    await executeQuery(
      `INSERT INTO admins (username, email, password_hash, is_active, created_at, updated_at)
       VALUES (?, ?, ?, 1, NOW(), NOW())`,
      [username, email, passwordHash]
    );

    // الحصول على بيانات المستخدم الجديد
    const newUser = await executeQuerySingle(
      'SELECT id, username, email, is_active, created_at FROM admins WHERE username = ?',
      [username]
    );

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء المستخدم بنجاح',
      user: newUser
    }, { status: 201 });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
