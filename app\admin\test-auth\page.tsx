'use client';

import React, { useEffect, useState } from 'react';
import { isAuthenticatedClient, getAuthTokenClient } from '../../../lib/auth';

const TestAuth = () => {
  type AuthInfo = {
    isAuthenticated: boolean;
    token: string | null;
    localToken: string | null;
    cookieToken: string | null;
    allCookies: string;
  };

  const [authInfo, setAuthInfo] = useState<AuthInfo>({
    isAuthenticated: false,
    token: null,
    localToken: null,
    cookieToken: null,
    allCookies: '',
  });

  useEffect(() => {
    const checkAuth = () => {
      const isAuth = isAuthenticatedClient();
      const token = getAuthTokenClient();
      const localToken = localStorage.getItem('authToken');
      const cookieToken = document.cookie
        .split('; ')
        .find(row => row.startsWith('authToken='))
        ?.split('=')[1];

      setAuthInfo({
        isAuthenticated: isAuth,
        token: token ? token.substring(0, 20) + '...' : null,
        localToken: localToken ? localToken.substring(0, 20) + '...' : null,
        cookieToken: cookieToken ? cookieToken.substring(0, 20) + '...' : null,
        allCookies: document.cookie
      });
    };

    checkAuth();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">اختبار المصادقة</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">معلومات المصادقة</h2>
          
          <div className="space-y-4">
            <div>
              <strong>مصادق:</strong> 
              <span className={authInfo.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                {authInfo.isAuthenticated ? ' نعم ✅' : ' لا ❌'}
              </span>
            </div>
            
            <div>
              <strong>التوكن الحالي:</strong> 
              <code className="bg-gray-100 px-2 py-1 rounded">
                {authInfo.token || 'غير موجود'}
              </code>
            </div>
            
            <div>
              <strong>localStorage Token:</strong> 
              <code className="bg-gray-100 px-2 py-1 rounded">
                {authInfo.localToken || 'غير موجود'}
              </code>
            </div>
            
            <div>
              <strong>Cookie Token:</strong> 
              <code className="bg-gray-100 px-2 py-1 rounded">
                {authInfo.cookieToken || 'غير موجود'}
              </code>
            </div>
            
            <div>
              <strong>جميع الكوكيز:</strong> 
              <code className="bg-gray-100 px-2 py-1 rounded block mt-2">
                {authInfo.allCookies || 'لا توجد كوكيز'}
              </code>
            </div>
          </div>
          
          <div className="mt-6">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              تحديث
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestAuth;
