import { NextResponse } from 'next/server';
import mysql, { RowDataPacket } from 'mysql2/promise';

// تعريف نوع البيانات لنتيجة COUNT
interface CountResult extends RowDataPacket {
  count: number;
}

// إعداد الاتصال بقاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

export async function GET() {

  let connection;

  try {
    console.log('🔄 اختبار الاتصال بقاعدة البيانات...');
    console.log('📊 إعدادات قاعدة البيانات:', {
      host: dbConfig.host,
      user: dbConfig.user,
      database: dbConfig.database,
      hasPassword: !!dbConfig.password
    });

    // محاولة الاتصال
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // اختبار الجداول الموجودة
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM information_schema.tables 
      WHERE table_schema = ?
    `, [dbConfig.database]);

    console.log('📋 الجداول الموجودة:', tables);

    // اختبار جدول المنتجات
    let productsTest = null;
    try {
      const [productsResult] = await connection.execute(`
        SELECT COUNT(*) as count FROM products LIMIT 1
      `);
      const rows = productsResult as CountResult[];
      productsTest = {
        exists: true,
        count: rows[0].count
      };
      console.log('📦 جدول المنتجات:', productsTest);
    } catch (error) {
      productsTest = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      console.log('❌ جدول المنتجات غير موجود:', error instanceof Error ? error.message : 'Unknown error');
    }

    // اختبار جدول الفئات
    let categoriesTest = null;
    try {
      const [categoriesResult] = await connection.execute(`
        SELECT COUNT(*) as count FROM categories LIMIT 1
      `);
      const rows = categoriesResult as CountResult[];
      categoriesTest = {
        exists: true,
        count: rows[0].count
      };
      console.log('📁 جدول الفئات:', categoriesTest);
    } catch (error) {
      categoriesTest = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      console.log('❌ جدول الفئات غير موجود:', error instanceof Error ? error.message : 'Unknown error');
    }

    // اختبار جدول المديرين
    let adminsTest = null;
    try {
      const [adminsResult] = await connection.execute(`
        SELECT COUNT(*) as count FROM admins LIMIT 1
      `);
      const rows = adminsResult as CountResult[];
      adminsTest = {
        exists: true,
        count: rows[0].count
      };
      console.log('👥 جدول المديرين:', adminsTest);
    } catch (error) {
      adminsTest = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      console.log('❌ جدول المديرين غير موجود:', error instanceof Error ? error.message : 'Unknown error');
    }

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      messageAr: 'تم الاتصال بقاعدة البيانات بنجاح',
      data: {
        config: {
          host: dbConfig.host,
          user: dbConfig.user,
          database: dbConfig.database,
          hasPassword: !!dbConfig.password
        },
        tables: tables,
        tests: {
          products: productsTest,
          categories: categoriesTest,
          admins: adminsTest
        }
      }
    });

  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Database connection failed',
      messageAr: 'فشل في الاتصال بقاعدة البيانات',
      error: {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: (error as { code?: string }).code,
        errno: (error as { errno?: number }).errno,
        sqlState: (error as { sqlState?: string }).sqlState
      },
      config: {
        host: dbConfig.host,
        user: dbConfig.user,
        database: dbConfig.database,
        hasPassword: !!dbConfig.password
      }
    }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
