import { revalidateTag, revalidatePath } from 'next/cache';

/**
 * مدير الكاش - لإدارة إعادة التحقق من البيانات المخزنة مؤقتاً
 */

// تعريف أنواع العلامات المختلفة
export type CacheTag = 
  | 'products' 
  | 'categories' 
  | 'subcategories' 
  | 'featured' 
  | 'settings'
  | 'quotes'
  | 'admins';

// تعريف المسارات المختلفة
export type CachePath = 
  | '/' 
  | '/products' 
  | '/admin' 
  | '/admin/products'
  | '/admin/categories'
  | '/admin/subcategories';

/**
 * إعادة التحقق من علامة معينة
 */
export function invalidateTag(tag: CacheTag): void {
  try {
    revalidateTag(tag);
    console.log(`✅ تم إعادة التحقق من العلامة: ${tag}`);
  } catch (error) {
    console.error(`❌ خطأ في إعادة التحقق من العلامة ${tag}:`, error);
  }
}

/**
 * إعادة التحقق من عدة علامات
 */
export function invalidateTags(tags: CacheTag[]): void {
  tags.forEach(tag => invalidateTag(tag));
}

/**
 * إعادة التحقق من مسار معين
 */
export function invalidatePath(path: CachePath): void {
  try {
    revalidatePath(path);
    console.log(`✅ تم إعادة التحقق من المسار: ${path}`);
  } catch (error) {
    console.error(`❌ خطأ في إعادة التحقق من المسار ${path}:`, error);
  }
}

/**
 * إعادة التحقق من عدة مسارات
 */
export function invalidatePaths(paths: CachePath[]): void {
  paths.forEach(path => invalidatePath(path));
}

/**
 * إعادة التحقق الشاملة للمنتجات
 */
export function invalidateProducts(): void {
  invalidateTags(['products', 'featured']);
  invalidatePaths(['/', '/products']);
}

/**
 * إعادة التحقق الشاملة للفئات
 */
export function invalidateCategories(): void {
  invalidateTags(['categories', 'products']);
  invalidatePaths(['/', '/products', '/admin/categories']);
}

/**
 * إعادة التحقق الشاملة للفئات الفرعية
 */
export function invalidateSubcategories(): void {
  invalidateTags(['subcategories', 'products']);
  invalidatePaths(['/', '/products', '/admin/subcategories']);
}

/**
 * إعادة التحقق الشاملة للإعدادات
 */
export function invalidateSettings(): void {
  invalidateTags(['settings']);
  invalidatePaths(['/', '/admin']);
}

/**
 * إعادة التحقق من جميع البيانات
 */
export function invalidateAll(): void {
  const allTags: CacheTag[] = ['products', 'categories', 'subcategories', 'featured', 'settings', 'quotes', 'admins'];
  const allPaths: CachePath[] = ['/', '/products', '/admin', '/admin/products', '/admin/categories', '/admin/subcategories'];
  
  invalidateTags(allTags);
  invalidatePaths(allPaths);
  
  console.log('🔄 تم إعادة التحقق من جميع البيانات المخزنة مؤقتاً');
}

/**
 * دالة مساعدة لتنظيف الكاش بناءً على نوع العملية
 */
export function handleCacheInvalidation(
  operation: 'create' | 'update' | 'delete',
  entity: 'product' | 'category' | 'subcategory' | 'setting'
): void {
  console.log(`🔄 معالجة إعادة التحقق للعملية: ${operation} على ${entity}`);
  
  switch (entity) {
    case 'product':
      invalidateProducts();
      break;
    case 'category':
      invalidateCategories();
      break;
    case 'subcategory':
      invalidateSubcategories();
      break;
    case 'setting':
      invalidateSettings();
      break;
    default:
      console.warn(`⚠️ نوع الكيان غير معروف: ${entity}`);
  }
}

/**
 * دالة للتحقق من صحة الكاش (للتطوير)
 */
export function validateCache(): void {
  console.log('🔍 فحص حالة الكاش...');
  // يمكن إضافة منطق فحص الكاش هنا
  console.log('✅ تم فحص الكاش بنجاح');
}
