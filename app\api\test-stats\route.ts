import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// إعداد الاتصال بقاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

// أنواع البيانات لنتائج الاستعلامات
interface ProductStats {
  total: number;
  active: number;
  featured: number;
}

interface CategoryStats {
  total: number;
  active: number;
}

interface AdminStats {
  total: number;
}

// نوع البيانات لأخطاء MySQL
interface MySQLError extends Error {
  code?: string;
  errno?: number;
  sqlState?: string;
  sqlMessage?: string;
}

export async function GET() {

  let connection;

  try {
    console.log('🔄 اختبار جلب الإحصائيات...');
    
    // إنشاء اتصال بقاعدة البيانات
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // إحصائيات المنتجات
    console.log('📦 جلب إحصائيات المنتجات...');
    const [productsResult] = await connection.execute(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured
      FROM products
    `) as [ProductStats[], mysql.FieldPacket[]];
    console.log('📦 نتيجة المنتجات:', productsResult[0]);

    // إحصائيات الفئات
    console.log('📁 جلب إحصائيات الفئات...');
    const [categoriesResult] = await connection.execute(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active
      FROM categories
    `) as [CategoryStats[], mysql.FieldPacket[]];
    console.log('📁 نتيجة الفئات:', categoriesResult[0]);

    // إحصائيات الفئات الفرعية
    console.log('📂 جلب إحصائيات الفئات الفرعية...');
    const [subcategoriesResult] = await connection.execute(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active
      FROM subcategories
    `) as [CategoryStats[], mysql.FieldPacket[]];
    console.log('📂 نتيجة الفئات الفرعية:', subcategoriesResult[0]);

    // إحصائيات المديرين
    console.log('👥 جلب إحصائيات المديرين...');
    const [adminsResult] = await connection.execute(`
      SELECT
        COUNT(*) as total
      FROM admins
    `) as [AdminStats[], mysql.FieldPacket[]];
    console.log('👥 نتيجة المديرين:', adminsResult[0]);

    // تجميع النتائج
    const stats = {
      products: {
        total: productsResult[0].total,
        active: productsResult[0].active,
        featured: productsResult[0].featured
      },
      categories: {
        total: categoriesResult[0].total,
        active: categoriesResult[0].active
      },
      subcategories: {
        total: subcategoriesResult[0].total,
        active: subcategoriesResult[0].active
      },
      admins: {
        total: adminsResult[0].total
      }
    };

    console.log('✅ الإحصائيات النهائية:', stats);

    return NextResponse.json({
      success: true,
      message: 'Stats fetched successfully',
      messageAr: 'تم جلب الإحصائيات بنجاح',
      stats
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الإحصائيات:', error);

    return NextResponse.json({
      success: false,
      message: 'Failed to fetch stats',
      messageAr: 'فشل في جلب الإحصائيات',
      error: error instanceof Error ? {
        message: error.message,
        code: (error as MySQLError).code,
        errno: (error as MySQLError).errno
      } : 'Unknown error'
    }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
