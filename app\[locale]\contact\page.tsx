import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getPageSEO } from '../../../lib/seo.config';
import Navbar from '../../../components/Navbar';
import ContactContent from '../../../components/ContactContent';

// إنشاء metadata لصفحة التواصل
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const seoData = getPageSEO(locale, 'contact');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}/contact`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-contact.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-contact.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}/contact`,
      languages: {
        'ar': `${baseUrl}/ar/contact`,
        'en': `${baseUrl}/en/contact`,
      },
    },
  };
}

export default async function ContactPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <ContactContent locale={locale} />
    </>
  );
}
