import { NextRequest, NextResponse } from 'next/server';
import {
  getQuoteRequestById,
  updateQuoteRequest,
  addQuoteRequestLog,
  getQuoteRequestWithDetails
} from '@/lib/mysql-database';
import { QuoteRequestUpdate } from '@/types/mysql-database';

// PATCH - تحديث حالة طلب التسعير
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { status, notes } = body;

    // التحقق من وجود الطلب في قاعدة البيانات
    const existingRequest = await getQuoteRequestById(id as string);
    if (!existingRequest) {
      return NextResponse.json({
        success: false,
        message: 'الطلب غير موجود'
      }, { status: 404 });
    }

      // تحديث بيانات الطلب في قاعدة البيانات
      const updateData: QuoteRequestUpdate = {};
      if (status !== undefined) updateData.status = status;
      if (notes !== undefined) updateData.notes = notes;

      const updatedRequest = await updateQuoteRequest(id as string, updateData);

      // إضافة سجل للتحديث إذا تم تغيير الحالة
      if (status && status !== existingRequest.status) {
        await addQuoteRequestLog(
          id as string,
          'admin', // يمكن تحسين هذا لاحقاً لاستخدام معرف المدير الفعلي
          'status_change',
          `تم تغيير الحالة من ${existingRequest.status} إلى ${status}`
        );
      }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الطلب بنجاح',
      request: updatedRequest
    });

  } catch (error) {
    console.error('Error updating quote request:', error);
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ أثناء تحديث الطلب'
    }, { status: 500 });
  }
}

// GET - جلب طلب تسعير بواسطة ID
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // جلب طلب التسعير مع التفاصيل من قاعدة البيانات
    const quoteRequest = await getQuoteRequestWithDetails(id as string);

    if (!quoteRequest) {
      return NextResponse.json({
        success: false,
        message: 'الطلب غير موجود'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      request: quoteRequest
    });

  } catch (error) {
    console.error('Error fetching quote request:', error);
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ أثناء جلب الطلب'
    }, { status: 500 });
  }
}
